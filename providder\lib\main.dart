import 'package:flutter/material.dart';
import 'package:providder/HomeScreenState.dart';
import 'package:providder/Statemangement/inherited_widget.dart';
import 'package:providder/theme.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    return CustemStateful(
      child: MaterialApp(
       theme: light_mode ,
        debugShowCheckedModeBanner: false,
        home: HomeScreen(),
      ),
    );
  }
}


