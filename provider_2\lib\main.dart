import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider_2/Pages/Homescreen.dart';
import 'package:provider_2/State_mangment/My_State.dart';
void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false ,
      title: 'Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: ChangeNotifierProvider<My_State>(
        create: (context) => My_State(),
        child:  MyHomePage(title: 'Flutter Demo Home Page')),
    );
  }
}


