import 'package:flutter/material.dart';
import 'package:providder/Second.dart';
import 'package:providder/Statemangement/inherited_widget.dart';
class HomeScreen extends StatefulWidget {
  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {

  @override
  Widget build(BuildContext context) {
    final state_ful = CustemInheritedWidget.of(context);
    return Scaffold(
      backgroundColor:state_ful.background_color,
      appBar: AppBar(
        title: Text('Counter App'),
        backgroundColor:state_ful.on_primary_color,
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              CustemInheritedWidget.of(context).cnt.toString(),
              style: TextStyle(
                fontSize: 35,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            My_Cust<PERSON>_<PERSON>ton(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          CustemInheritedWidget.of(context).inc_cnt();
        },
        child: Icon(Icons.add , color: Colors.white,),
        backgroundColor:state_ful.secondary_color,
      ),
    );
  }
}

class My_Custem_Button extends StatelessWidget {
  const My_Custem_Button({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final state_ful = CustemInheritedWidget.of(context);
    return Container(
      height: 50,
      width: 300,
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => Second(),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor:state_ful.primary_color,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: Text(
          'Go To The Next Screen',
          style: TextStyle(fontSize: 18, color: Colors.black),
        ),
      ),
    );
  }
}
