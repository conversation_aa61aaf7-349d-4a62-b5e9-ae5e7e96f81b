import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:listview/Manage_State/My_State.dart';
import 'package:listview/Pages/Homescreen.dart';
import 'package:listview/models/task.dart';
import 'package:provider/provider.dart';

void main() async {
  // Initialize Flutter
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Register the Task adapter
  Hive.registerAdapter(TaskAdapter());

  // Open the tasks box
  await Hive.openBox<Task>('tasksBox');

  runApp(
     ChangeNotifierProvider<My_State>(
       create: (context) => My_State(),
       child: MaterialApp(
        debugShowCheckedModeBanner: false,
        home: Homescreen()
       ),
     ),
  );
}
