import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:listview/list.dart';
import 'package:listview/models/task.dart';

class My_State with ChangeNotifier {
  bool st = false;
  String text_inp = '', text_ = '', tex = ' ', tex_ = ' ';
  List<My_list> tasks = [];
  int len = 0;
  Color color = Colors.white, new_color = Colors.white;
  bool ch1 = false, ch2 = false;

  // Reference to the Hive box
  final Box<Task> _tasksBox = Hive.box<Task>('tasksBox');

  My_State() {
    // Load tasks from Hive when the state is initialized
    _loadTasksFromHive();
  }

  // Load tasks from Hive
  void _loadTasksFromHive() {
    if (_tasksBox.isNotEmpty) {
      final hiveTasks = _tasksBox.values.toList();

      // Sort tasks by index
      hiveTasks.sort((a, b) => a.index_.compareTo(b.index_));

      // Convert Hive tasks to My_list tasks
      tasks = hiveTasks.map((task) => My_list(
        color: task.color,
        text: task.text,
        ch_: task.ch_,
        text_: task.text_,
        index_: task.index_,
      )).toList();

      // Update the length counter
      len = tasks.where((task) => !task.ch_).length;

      notifyListeners();
    }
  }

  // Save tasks to Hive
  void _saveTasksToHive() {
    // Clear the box first
    _tasksBox.clear();

    // Add all tasks to the box
    for (var task in tasks) {
      _tasksBox.add(Task.fromMyList(task));
    }
  }

  void delete_task(int index) {
    tasks.removeAt(index);
    if (len > 0) len--;

    // Update indices of remaining tasks
    for (int i = 0; i < tasks.length; i++) {
      tasks[i].index_ = i;
    }

    // Save changes to Hive
    _saveTasksToHive();

    notifyListeners();
  }

  void update_task(int index) {
    if (ch1) tasks[index].text = tex;
    if (ch2) {
      tasks[index].color = new_color;
      tasks[index].text_ = tex_;
    }
    if (ch1 && ch2) {
      tasks[index].text = tex;
      tasks[index].color = new_color;
      tasks[index].text_ = tex_;
    }

    // Save changes to Hive
    _saveTasksToHive();

    notifyListeners();
  }

  void add_tasks() {
    tasks.add(My_list(
      color: color,
      text: text_inp,
      ch_: st,
      text_: text_,
      index_: tasks.length
    ));
    len++;

    // Save changes to Hive
    _saveTasksToHive();

    notifyListeners();
  }

  void check() {
    // Update the length counter
    len = tasks.where((task) => !task.ch_).length;

    // Save changes to Hive
    _saveTasksToHive();

    notifyListeners();
  }
}