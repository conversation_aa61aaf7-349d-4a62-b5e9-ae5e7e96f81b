import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider_2/State_mangment/My_State.dart';
class MyHomePage extends StatelessWidget {
   MyHomePage({super.key, required this.title});
  final String title;
  @override
  Widget build(BuildContext context) {
    final cnt_state = Provider.of<My_State>(context); 
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text(
              'You have pushed the button this many times:',
            ),
            Text(
              '${cnt_state.cnt}',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: cnt_state.inc_cnt,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}