import 'package:flutter/material.dart';
import 'package:providder/theme.dart';
class CustemStateful extends StatefulWidget {
  final Widget child;
   CustemStateful({super.key , required this.child});

  @override
  State<CustemStateful> createState() => _CustemStatefulState();
}

class _CustemStatefulState extends State<CustemStateful> {
  var background_color = light_mode.colorScheme.surface;
  var primary_color = light_mode.colorScheme.primary;
  var secondary_color = light_mode.colorScheme.secondary;
  var on_primary_color = light_mode.colorScheme.onPrimary;
  int cnt = 0;
  
  inc_cnt() {
    setState(() {
      cnt++;
    });
  }
  
  Change_to_firstmod() {
    setState(() {
      background_color = light_mode.colorScheme.surface;
      primary_color = light_mode.colorScheme.primary;
      secondary_color = light_mode.colorScheme.secondary;
      on_primary_color = light_mode.colorScheme.onPrimary;
    });
  }
  
  Change_to_secondmod() {
    setState(() {
      background_color = dark_mode.colorScheme.surface;
      primary_color = dark_mode.colorScheme.primary;
      secondary_color = dark_mode.colorScheme.secondary;
      on_primary_color = dark_mode.colorScheme.onPrimary;
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustemInheritedWidget(child: widget.child, cnt: cnt, state: this);
  }
}
class CustemInheritedWidget extends InheritedWidget {
  final int cnt;
  final _CustemStatefulState state;
  
  CustemInheritedWidget({required super.child, required this.cnt, required this.state});
  
  static _CustemStatefulState of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<CustemInheritedWidget>()!.state;
  }
  
  @override
  bool updateShouldNotify(covariant CustemInheritedWidget oldWidget) {
    return oldWidget.cnt != cnt || 
           oldWidget.state.background_color != state.background_color ;
  }
}
