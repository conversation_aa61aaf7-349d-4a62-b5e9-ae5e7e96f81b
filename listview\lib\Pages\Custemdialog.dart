import 'package:flutter/material.dart';
import 'package:listview/Manage_State/My_State.dart';
import 'package:provider/provider.dart';
class My_Custem_Dialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              spreadRadius: 5,
              blurRadius: 15,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إضافة مهمة جديدة',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
              ),
            ),
            SizedBox(height: 30),
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Consumer<My_State>(
                builder: (context, state, child) => TextField(
                  onChanged: (value) => state.text_inp = value,
                decoration: InputDecoration(
                  hintText: 'اكتب مهمتك هنا...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(20),
                ),
                textAlign: TextAlign.right,
              ),
            ),
            ) ,
SizedBox(height: 30,) ,
            Text(
              'مستوى الأهمية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
            ),
            SizedBox(height: 20),
            Consumer<My_State>(
              builder: (context, state, child) => Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  PriorityCircle(
                    state: state,
                    color: Colors.red,
                    text: 'عاجل',
                    icon: Icons.priority_high,
                  ),
                  PriorityCircle(
                    state: state,
                    color: Colors.orange,
                    text: 'مهم',
                    icon: Icons.star,
                  ),
                  PriorityCircle(
                    state: state,
                    color: Colors.green,
                    text: 'عادي',
                    icon: Icons.check_circle,
                  ),
                ],
              ),
            ),
            SizedBox(height: 30),
            Consumer<My_State>(
              builder: (context, state, child) => ElevatedButton(
                onPressed: () {
                  state.add_tasks();
                  Navigator.pop(context);
                },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                padding: EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                'إضافة المهمة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white ,
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }
}

class PriorityCircle extends StatelessWidget {
  final My_State state;
  final Color color;
  final String text;
  final IconData icon;

  const PriorityCircle({
    required this.state,
    required this.color,
    required this.text,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10),
      child: InkWell(
        onTap: () {
          state.color = color;
          state.text_ = text;
        },
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 30),
            ),
            SizedBox(height: 8),
            Text(
              text,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
