import 'package:flutter/material.dart';
import 'package:providder/HomeScreenState.dart';
import 'package:providder/Statemangement/inherited_widget.dart';
class Second extends StatefulWidget {
   Second({super.key});

  @override
  State<Second> createState() => _SecondState();
}
class _SecondState extends State<Second> {
  void pub_screen(BuildContext context) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => HomeScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    final state_ful = CustemInheritedWidget.of(context);
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              state_ful.cnt.toString(),
              style: TextStyle(
                fontSize: 35,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            C<PERSON><PERSON><PERSON>utton(
              color: Colors.amber,
              text: 'First Mode', 
              action: () {
                state_ful.Change_to_firstmod();
                pub_screen(context);
              },
            ),
            <PERSON>zedBox(height: 20),
            <PERSON><PERSON><PERSON><PERSON><PERSON>on(
              color: Colors.blue.shade300,
              text: 'Second Mode', 
              action: () {
                state_ful.Change_to_secondmod();
                pub_screen(context);
              },
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => state_ful.inc_cnt(),
        child: Icon(Icons.add, color: Colors.white),
        backgroundColor: Colors.amber,
      ),
    );
  }
}

class CustemButton extends StatelessWidget {
  Function action ;
  Color color ; String text ;
   CustemButton({
    super.key, required this.color , required this.text , 
    required this.action
  });

  @override
  Widget build(BuildContext context) {
    return Container(
     height: 50,
     width: 300,
     child: ElevatedButton(
       onPressed: ()=>action(),
       style: ElevatedButton.styleFrom(
         backgroundColor: color,
         shape: RoundedRectangleBorder(
           borderRadius: BorderRadius.circular(10),
         ),
       ),
       child: Text(
        text,
         style: TextStyle(fontSize: 18, color: Colors.black),
       ),
     ),
                );
  }
}
